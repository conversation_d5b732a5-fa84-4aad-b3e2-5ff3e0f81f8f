import { type NextRequest, NextResponse } from "next/server"
import crypto from "crypto"

// 币安API基础URL
const BINANCE_API_BASE = "https://api.binance.com"

// Alpha代币列表
const ALPHA_TOKENS = ["BR", "KOGE", "ZKJ", "ALPHA", "BETA", "GAMMA"]

function createSignature(queryString: string, secretKey: string): string {
  return crypto.createHmac("sha256", secretKey).update(queryString).digest("hex")
}

export async function POST(request: NextRequest) {
  try {
    const { apiKey, secretKey } = await request.json()

    if (!apiKey || !secretKey) {
      return NextResponse.json({ error: "API Key和Secret Key不能为空" }, { status: 400 })
    }

    // 首先测试API凭据是否有效
    const testTimestamp = Date.now()
    const testQueryString = `timestamp=${testTimestamp}`
    const testSignature = createSignature(testQueryString, secretKey)

    try {
      const testResponse = await fetch(`${BINANCE_API_BASE}/api/v3/account?${testQueryString}&signature=${testSignature}`, {
        headers: {
          "X-MBX-APIKEY": apiKey,
        },
        timeout: 10000,
      })

      if (!testResponse.ok) {
        const errorData = await testResponse.json()
        return NextResponse.json({
          error: `API凭据验证失败: ${errorData.msg || "请检查API Key和Secret Key是否正确"}`
        }, { status: 401 })
      }
    } catch (error) {
      console.error("API凭据测试失败:", error)
      return NextResponse.json({
        error: "无法连接到币安API，请检查网络连接或API凭据"
      }, { status: 500 })
    }

    // 获取当前UTC日期的开始和结束时间戳
    const now = new Date()
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000 - 1)

    const startTime = startOfDay.getTime()
    const endTime = endOfDay.getTime()

    // 构建查询参数
    const timestamp = Date.now()
    const queryString = `timestamp=${timestamp}&startTime=${startTime}&endTime=${endTime}`
    const signature = createSignature(queryString, secretKey)

    // 获取Alpha代币交易数据
    const alphaTradesMap = new Map<string, { volume: number; points: number }>()

    // 为每个Alpha代币获取交易历史
    for (const token of ALPHA_TOKENS) {
      const symbol = `${token}USDT`

      try {
        const tokenQueryString = `symbol=${symbol}&${queryString}`
        const tokenSignature = createSignature(tokenQueryString, secretKey)

        const response = await fetch(`${BINANCE_API_BASE}/api/v3/myTrades?${tokenQueryString}&signature=${tokenSignature}`, {
          headers: {
            "X-MBX-APIKEY": apiKey,
          },
          timeout: 10000, // 10秒超时
        })

        if (response.ok) {
          const trades = await response.json()

          if (trades && trades.length > 0) {
            // 计算该代币的总交易量
            let totalVolume = 0
            trades.forEach((trade: any) => {
              totalVolume += Number.parseFloat(trade.quoteQty) // USDT交易量
            })

            if (totalVolume > 0) {
              const adjustedVolume = totalVolume * 4 // 最终交易量需要乘以4
              const points = adjustedVolume >= 2 ? Math.floor(Math.log2(adjustedVolume / 2)) + 1 : 0
              alphaTradesMap.set(symbol, { volume: totalVolume, points })
            }
          }
        }
      } catch (error) {
        console.error(`获取${symbol}交易数据失败:`, error)
        // 继续处理其他代币，不中断整个流程
      }
    }

    // 转换为数组格式
    const tradeData = Array.from(alphaTradesMap.entries()).map(([symbol, data]) => ({
      symbol,
      volume: data.volume,
      points: data.points,
    }))

    const totalPoints = tradeData.reduce((sum, trade) => sum + trade.points, 0)

    return NextResponse.json({
      trades: tradeData,
      totalPoints,
      date: startOfDay.toISOString().split("T")[0],
    })
  } catch (error) {
    console.error("API Error:", error)
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 })
  }
}
