import { type NextRequest, NextResponse } from "next/server"
import crypto from "crypto"

// 币安API基础URL
const BINANCE_API_BASE = "https://api.binance.com"

// Alpha代币列表
const ALPHA_TOKENS = ["BR", "K<PERSON>GE", "ZKJ", "ALPHA", "BETA", "GAMMA"]

function createSignature(queryString: string, secretKey: string): string {
  return crypto.createHmac("sha256", secretKey).update(queryString).digest("hex")
}

export async function POST(request: NextRequest) {
  try {
    const { apiKey, secretKey } = await request.json()

    if (!apiKey || !secretKey) {
      return NextResponse.json({ error: "API Key和Secret Key不能为空" }, { status: 400 })
    }

    // 获取当前UTC日期的开始和结束时间戳
    const now = new Date()
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000 - 1)

    const startTime = startOfDay.getTime()
    const endTime = endOfDay.getTime()

    // 构建查询参数
    const timestamp = Date.now()
    const queryString = `timestamp=${timestamp}&startTime=${startTime}&endTime=${endTime}`
    const signature = createSignature(queryString, secretKey)

    // 调用币安API获取交易历史
    const response = await fetch(`${BINANCE_API_BASE}/api/v3/myTrades?${queryString}&signature=${signature}`, {
      headers: {
        "X-MBX-APIKEY": apiKey,
      },
    })

    if (!response.ok) {
      const errorData = await response.json()
      return NextResponse.json({ error: errorData.msg || "获取交易数据失败" }, { status: response.status })
    }

    const trades = await response.json()

    // 筛选Alpha代币交易并计算积分
    const alphaTradesMap = new Map<string, { volume: number; points: number }>()

    trades.forEach((trade: any) => {
      const symbol = trade.symbol
      const baseAsset = symbol.replace("USDT", "")

      if (ALPHA_TOKENS.includes(baseAsset)) {
        const volume = Number.parseFloat(trade.quoteQty) // USDT交易量
        const adjustedVolume = volume * 4 // 最终交易量需要乘以4
        const points = adjustedVolume >= 2 ? Math.floor(Math.log2(adjustedVolume / 2)) + 1 : 0

        if (alphaTradesMap.has(symbol)) {
          const existing = alphaTradesMap.get(symbol)!
          existing.volume += volume
          existing.points = existing.volume * 4 >= 2 ? Math.floor(Math.log2((existing.volume * 4) / 2)) + 1 : 0
        } else {
          alphaTradesMap.set(symbol, { volume, points })
        }
      }
    })

    // 转换为数组格式
    const tradeData = Array.from(alphaTradesMap.entries()).map(([symbol, data]) => ({
      symbol,
      volume: data.volume,
      points: data.points,
    }))

    const totalPoints = tradeData.reduce((sum, trade) => sum + trade.points, 0)

    return NextResponse.json({
      trades: tradeData,
      totalPoints,
      date: startOfDay.toISOString().split("T")[0],
    })
  } catch (error) {
    console.error("API Error:", error)
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 })
  }
}
