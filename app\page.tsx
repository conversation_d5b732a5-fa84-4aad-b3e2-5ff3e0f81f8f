"use client"

import { useState, useEffect } from "react"
import { Moon, Sun, Calculator, TrendingUp, Wallet, Shield } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useTheme } from "next-themes"

interface TradeData {
  symbol: string
  volume: number
  points: number
}

interface ApiCredentials {
  apiKey: string
  secretKey: string
}

const ALPHA_TOKENS = ["BR", "KOGE", "ZKJ", "ALPHA", "BETA", "GAMMA"]

export default function AlphaPointsTracker() {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  const [credentials, setCredentials] = useState<ApiCredentials>({ apiKey: "", secretKey: "" })
  const [isLoading, setIsLoading] = useState(false)
  const [tradeData, setTradeData] = useState<TradeData[]>([])
  const [totalPoints, setTotalPoints] = useState(0)
  const [error, setError] = useState("")

  useEffect(() => {
    setMounted(true)
    // Load saved credentials from localStorage
    const saved = localStorage.getItem("binance-credentials")
    if (saved) {
      setCredentials(JSON.parse(saved))
    }
  }, [])

  const calculatePoints = (volume: number): number => {
    const adjustedVolume = volume * 4 // 最终交易量需要乘以4
    if (adjustedVolume < 2) return 0

    // 对数计算：2$ = 1积分, 4$ = 2积分, 8$ = 3积分, etc.
    return Math.floor(Math.log2(adjustedVolume / 2)) + 1
  }

  const saveCredentials = () => {
    localStorage.setItem("binance-credentials", JSON.stringify(credentials))
  }

  const fetchTradeHistory = async () => {
    if (!credentials.apiKey || !credentials.secretKey) {
      setError("请输入API Key和Secret Key")
      return
    }

    setIsLoading(true)
    setError("")

    try {
      // 模拟API调用 - 在实际应用中，这里会调用币安API
      // 注意：由于安全原因，实际的币安API调用应该通过后端进行
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // 模拟交易数据
      const mockData: TradeData[] = [
        { symbol: "BRUSDT", volume: 156.78, points: calculatePoints(156.78) },
        { symbol: "KOGEUSDT", volume: 89.45, points: calculatePoints(89.45) },
        { symbol: "ZKJUSDT", volume: 234.12, points: calculatePoints(234.12) },
        { symbol: "ALPHAUSDT", volume: 67.89, points: calculatePoints(67.89) },
      ]

      setTradeData(mockData)
      setTotalPoints(mockData.reduce((sum, trade) => sum + trade.points, 0))
      saveCredentials()
    } catch (err) {
      setError("获取交易历史失败，请检查API凭据")
    } finally {
      setIsLoading(false)
    }
  }

  if (!mounted) return null

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <header className="border-b bg-background/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Calculator className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-semibold">Alpha积分统计</h1>
              <p className="text-sm text-muted-foreground">币安交易积分计算工具</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            className="rounded-full"
          >
            {theme === "dark" ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
          </Button>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="space-y-8">
          {/* API设置卡片 */}
          <Card className="border-0 shadow-lg bg-card/50 backdrop-blur-sm">
            <CardHeader className="text-center pb-6">
              <CardTitle className="text-2xl font-semibold">API凭据设置</CardTitle>
              <CardDescription className="text-base">
                请输入您的币安API Key和Secret Key（仅需只读权限）
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Alert>
                <Shield className="h-4 w-4" />
                <AlertDescription>您的API凭据将安全存储在本地浏览器中，不会上传到任何服务器</AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="apiKey" className="text-sm font-medium">
                    API Key
                  </Label>
                  <Input
                    id="apiKey"
                    type="password"
                    placeholder="输入您的币安API Key"
                    value={credentials.apiKey}
                    onChange={(e) => setCredentials((prev) => ({ ...prev, apiKey: e.target.value }))}
                    className="h-12"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="secretKey" className="text-sm font-medium">
                    Secret Key
                  </Label>
                  <Input
                    id="secretKey"
                    type="password"
                    placeholder="输入您的币安Secret Key"
                    value={credentials.secretKey}
                    onChange={(e) => setCredentials((prev) => ({ ...prev, secretKey: e.target.value }))}
                    className="h-12"
                  />
                </div>
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Button
                onClick={fetchTradeHistory}
                disabled={isLoading || !credentials.apiKey || !credentials.secretKey}
                className="w-full h-12 text-base font-medium"
                size="lg"
              >
                {isLoading ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    <span>获取交易数据中...</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <Wallet className="w-5 h-5" />
                    <span>获取今日交易数据</span>
                  </div>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* 交易数据显示区域 */}
          {tradeData.length > 0 && (
            <div className="space-y-6">
              {/* 总积分卡片 */}
              <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-500/10 to-purple-600/10 backdrop-blur-sm">
                <CardContent className="p-8 text-center">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground">今日总积分</p>
                    <p className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                      {totalPoints}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      基于UTC时间 {new Date().toISOString().split("T")[0]} 的交易数据
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* 交易详情 */}
              <Card className="border-0 shadow-lg bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <TrendingUp className="w-5 h-5" />
                    <span>Alpha代币交易详情</span>
                  </CardTitle>
                  <CardDescription>今日Alpha代币现货交易统计</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {tradeData.map((trade, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-4 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center">
                            <span className="text-white font-semibold text-sm">
                              {trade.symbol.replace("USDT", "")}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium">{trade.symbol}</p>
                            <p className="text-sm text-muted-foreground">交易量: ${trade.volume.toFixed(2)} USDT</p>
                          </div>
                        </div>
                        <Badge variant="secondary" className="text-base px-3 py-1">
                          {trade.points} 积分
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* 积分计算规则 */}
              <Card className="border-0 shadow-lg bg-card/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle>积分计算规则</CardTitle>
                  <CardDescription>基于交易量的积分计算方式</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                    <div className="text-center p-3 rounded-lg bg-muted/30">
                      <p className="font-medium">$2</p>
                      <p className="text-muted-foreground">1积分</p>
                    </div>
                    <div className="text-center p-3 rounded-lg bg-muted/30">
                      <p className="font-medium">$4</p>
                      <p className="text-muted-foreground">2积分</p>
                    </div>
                    <div className="text-center p-3 rounded-lg bg-muted/30">
                      <p className="font-medium">$8</p>
                      <p className="text-muted-foreground">3积分</p>
                    </div>
                    <div className="text-center p-3 rounded-lg bg-muted/30">
                      <p className="font-medium">$16</p>
                      <p className="text-muted-foreground">4积分</p>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground mt-4 text-center">* 最终交易量 = 实际交易量 × 4</p>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t bg-background/80 backdrop-blur-sm mt-16">
        <div className="container mx-auto px-4 py-6 text-center text-sm text-muted-foreground">
          <p>Alpha积分统计工具 - 安全、简洁、高效</p>
        </div>
      </footer>
    </div>
  )
}
